
<!DOCTYPE HTML>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>DIG</title>

</head>
<body>
<!--
rsync -aP /WD/Freedom/App/php/dig/index.php /wks/h/dig/;
cd /wks/h/dig/
  chown root:www-data -R .
find .  -type d -exec chmod 750 {} \;
find .  -type f -exec chmod 640 {} \;

-->

  <div class="search-lr">
  <form method="post" action="dig.php" target="_blank">
    <input type="hidden" name="formhash" value="231f432c" >
    <h1>懒人外贸开发工具<img src="/images/default/dig.gif" alt="懒人不懒" title="懒人不懒"></h1>
    <p class="title-f"><b>思路</b>：找公司→找人→挖邮箱；<b>手段</b>：搜索→猜测→验证</p>
    <div class="input-row">
    <span class="search-arrow"></span>
    <input name="okeywords" type="text"  placeholder="请输入网址，也可输入公司名" value="" size="40" class="search-text"><input name="submit" id="submit" class="buttonstyle" type="submit" value="提交"></div>

<!--<p> Company :<input type="text" name="title"> <input type="submit" value=" Start Dig "></p>-->
<p>
<?php

	$str = $_POST['okeywords'];

  $QUOTE = '"';
	$google = 'https://www.google.com/search?q=';
	$google_en = 'https://www.google.com/search?hl=en&newwindow=1&lr=lang_en&cr=countryUS&num=100&q=';
	$google_enip = 'https://www.google.com/search?hl=en&num=100&newwindow=1&filter=0&ip=0.0.0.0&source_ip=0.0.0.0&lr=lang_en&cr=countryUS&q=';

	$bing_en = 'https://www.bing.com/search?hl=en&num=100&newwindow=1&filter=0&ip=0.0.0.0&source_ip=0.0.0.0&lr=lang_en&cr=countryUS&q=';
	$bing_enip = 'https://www.bing.com/search?hl=en&num=100&newwindow=1&filter=0&ip=0.0.0.0&source_ip=0.0.0.0&lr=lang_en&cr=countryUS&q=';

	$duckduckgo = 'https://duckduckgo.com/search?hl=en&num=100&newwindow=1&filter=0&ip=0.0.0.0&source_ip=0.0.0.0&lr=lang_en&cr=countryUS&q=';

// custom google: (hlsnxxx)
$Google_cseh= 'https://cse.google.com/cse?cx=c65adde800b2e2cc8#gsc.tab=0&gsc.q=';

echo 'Custom Google: ' . '<a href="' . $Google_cseh . $str . '&gsc.sort=' . $QUOTE . 'target="blank"' . '>' . Linkedin . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $str . $Association . 'target="blank"' . '>' . Association . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $str . $Exhibitor . 'target="blank"' . '>' . Exhibitor . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $Xlsx . $str . $QUOTE . 'target="blank"' . '>' . Excel . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $Pdf . $str . $QUOTE . 'target="blank"' . '>' . PDF . '</a>' . ' |▓ ' . '<a href="' . $google_en . $Anchor . $str . $QUOTE . 'target="blank"' . '>' . Anchor . '</a><br >';


//zoominfo
	$zoominfo = ' site:zoominfo.com/p (~marketing | ~purchasing | ~buyer | ~merchandise | ~vp | ~ceo | ~owner) -china"';

	echo 'zoominfo: ' . '<a href="' . $google_en . $str . $zoominfo . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $zoominfo . 'target="blank"' . '>' . bing . '</a><br >';

//trade fairs
	$Fairs = ' ~fair (Attendee|Exhibitor) ~Directory (Frankfurt| Hannover| Paris| Dusseldorf| Cologne| Milan| Munich| Barcelona| Berlin| Bologna| Basel| Nuremberg| LasVegas| Chicago| Verona| Rimini| Leipzig| Essen| London| Lyon| Birmingham| Madrid| Stuttgart| NewYork) 2014..2021"';

	echo 'Trade Fairs: ' . '<a href="' . $google_enip . $str . $Fairs . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $Fairs . 'target="blank"' . '>' . bing . '</a>' . ' |▓ ' . '<a href="' . $duckduckgo . $str . $zoominfo . 'target="blank"' . '>' . duckduckgo . '</a><br >';

//主流B2B
	$B2B = ' site:alibaba.com OR site:globalsources.com OR site:made-in-china.com OR site:tradekey.com"';
	$google_b2b = 'https://www.google.com/search?hl=en&num=100&newwindow=1&filter=0&ip=0.0.0.0&source_ip=0.0.0.0&lr=lang_en&cr=countryUS&q=';

	echo 'B2B: ' . '<a href="' . $google_b2b . $str . $B2B . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $B2B . 'target="blank"' . '>' . bing . '</a><br >';



	//海关数据
	//北美海关数据
  $Custom_data = ' +Automated+Manifest+System site:clustrmaps.com"';
	$portexaminer = 'http://portexaminer.com/search.php?search-field-1=cargo&search-term-1=';
		$portexaminer_shipper = 'http://portexaminer.com/search.php?search-field-1=shipper&search-term-1=';
		$portexaminer_consignee = 'http://portexaminer.com/search.php?search-field-1=consignee&search-term-1=';
		$paktradeinfo = ' http://www.paktradeinfo.com/international-trade/pakistan/import/4/';

	echo 'Custom_data: ' . '<a href="' . $google . $str . $Custom_data . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $Custom_data . 'target="blank"' . '>' . bing . '</a>' . ' |▓ ' . '<a href="' . $portexaminer . $str . '"' . 'target="_blank"' . '>' . Product . '</a>' . ' |▓ ' . '<a href="' . $portexaminer_shipper . $str . '"' . 'target="blank"' . '>' . Shipper . '</a>' . ' |▓ ' . '<a href="' . $portexaminer_consignee . $str . '"' . 'target="blank"' . '>' . Consignee . '</a>' . ' |▓ ' . '<a href="' . $paktradeinfo . $str . '"' . 'target="blank"' . '>' . 'US Importer' . '</a><br >';

//用Mapquest地图搜索北美xx商家
$mapquest = ' https://www.mapquest.com/search/results?query=';

	echo 'Mapquest: ' . '<a href="' . $mapquest . $str . $QUOTE . 'target="blank"' . '>' . Mapquest . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $B2B . 'target="blank"' . '>' . bing . '</a><br >';

//linkedin
$Linkedin = ' site:*.linkedin.com/pub/ OR site:*.linkedin.com/in/ -intitle:Profiles (~marketing | ~purchasing | ~buyer | ~merchandise | ~vp | ~ceo | ~owner)"';
$Linkedin_SME = ' site:*.linkedin.com/pub/ OR site:*.linkedin.com/in/ -intitle:Profiles"';
$Linkedin_SME2 = ' -intitle:profiles"';
$Google_cse = 'https://cse.google.com/cse?cx=8075caae382cc641b#gsc.tab=0&gsc.q=';
$Linkedin_profile = 'site:linkedin.com/company/ intitle:';
$Linkedin_company = ' site:linkedin.com/company/"';
$Avatar = ' (~marketing | ~purchasing | ~buyer | ~merchandise | ~vp | ~ceo | ~owner) imagesize:200x200"';
$LINKEDIN = ' site:www.linkedin.com/in';

echo 'linkedin big corp: ' . '<a href="' . $google_en . $str . $Linkedin . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $Linkedin . 'target="blank"' . '>' . bing . '</a>' . ' |▓ ' . '<a href="' . $google_en . $str . $Linkedin_SME . 'target="blank"' . '>' . SME . '</a>' . ' |▓ ' . '<a href="' . $Google_cse . $str . $Linkedin_SME2 . 'target="blank"' . '>' . SME2 . '</a>' . ' |▓ ' . '<a href="' . $google_en . $Linkedin_profile . $str . $QUOTE . 'target="blank"' . '>' . 'Co. Profiles' . '</a>' . ' |▓ ' . '<a href="' . $google . $LINKEDIN . $str . $Avatar . 'target="blank"' . '>' . 'Avatar' . '</a>' . ' |▓ ' . '<a href="' . $google . $str . $Linkedin_company . 'target="blank"' . '>' . 'Company' . '</a><br>';

//Facebook
$Facebook = ' others named site:facebook.com"';
$Facebook_people = 'https://www.facebook.com/search/people/?q=';

echo 'Facebook: ' . '<a href="' . $google_en . $str . $Facebook . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $Facebook . 'target="blank"' . '>' . bing . '</a>' . ' |▓ ' . '<a href="' . $Facebook_people . $str .  '&epa=SERP_TAB' . $QUOTE . 'target="blank"' . '>' . 'FB company name' . '</a><br >';

//bloomberg
$Bloomberg = '  site:bloomberg.com/profiles/people OR site:bloomberg.com/research/stocks/people OR site:bloomberg.com/research/stocks/private/person"';

echo 'Bloomberg: ' . '<a href="' . $google_en . $str . $Bloomberg . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $bing_enip . $str . $Bloomberg . 'target="blank"' . '>' . bing . '</a><br >';

//
$Association = ' inurl:association"';
$Exhibitor = ' inurl:exhibitor"';
$Xlsx = 'filetype:xlsx list ';
$Pdf = 'filetype:pdf list ';
$Anchor = 'allinanchor:';

echo 'AIHIT(gov registrated co): ' . '<a href="' . $Google_cse . $str . '&gsc.sort=' . $QUOTE . 'target="blank"' . '>' . Google . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $str . $Association . 'target="blank"' . '>' . Association . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $str . $Exhibitor . 'target="blank"' . '>' . Exhibitor . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $Xlsx . $str . $QUOTE . 'target="blank"' . '>' . Excel . '</a>' . ' |▓ ' . '<a href="' . $google_enip . $Pdf . $str . $QUOTE . 'target="blank"' . '>' . PDF . '</a>' . ' |▓ ' . '<a href="' . $google_en . $Anchor . $str . $QUOTE . 'target="blank"' . '>' . Anchor . '</a><br >';


//test
echo "hello", "world";
?>
</p>

</body>
</html>
