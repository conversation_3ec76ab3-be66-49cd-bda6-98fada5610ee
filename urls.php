<?php
// Configuration
$listsDir = 'url_lists';
$maxListNameLength = 50;
$maxUrlsPerList = 100;

// Create directory if it doesn't exist
if (!is_dir($listsDir)) {
    mkdir($listsDir, 0755, true);
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'save_list':
            $listName = trim($input['name'] ?? '');
            $urls = $input['urls'] ?? [];
            
            if (empty($listName) || empty($urls)) {
                echo json_encode(['success' => false, 'message' => 'List name and URLs are required']);
                exit;
            }
            
            if (strlen($listName) > $maxListNameLength) {
                echo json_encode(['success' => false, 'message' => 'List name too long']);
                exit;
            }
            
            if (count($urls) > $maxUrlsPerList) {
                echo json_encode(['success' => false, 'message' => 'Too many URLs']);
                exit;
            }
            
            // Sanitize filename
            $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $listName);
            $filepath = $listsDir . '/' . $filename . '.json';
            
            $data = [
                'name' => $listName,
                'urls' => $urls,
                'created' => date('Y-m-d H:i:s'),
                'count' => count($urls)
            ];
            
            if (file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT))) {
                echo json_encode(['success' => true, 'message' => 'List saved successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to save list']);
            }
            exit;
            
        case 'load_list':
            $listName = trim($input['name'] ?? '');
            if (empty($listName)) {
                echo json_encode(['success' => false, 'message' => 'List name required']);
                exit;
            }
            
            $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $listName);
            $filepath = $listsDir . '/' . $filename . '.json';
            
            if (file_exists($filepath)) {
                $data = json_decode(file_get_contents($filepath), true);
                echo json_encode(['success' => true, 'data' => $data]);
            } else {
                echo json_encode(['success' => false, 'message' => 'List not found']);
            }
            exit;
            
        case 'delete_list':
            $listName = trim($input['name'] ?? '');
            if (empty($listName)) {
                echo json_encode(['success' => false, 'message' => 'List name required']);
                exit;
            }
            
            $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $listName);
            $filepath = $listsDir . '/' . $filename . '.json';
            
            if (file_exists($filepath) && unlink($filepath)) {
                echo json_encode(['success' => true, 'message' => 'List deleted successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to delete list']);
            }
            exit;
            
        case 'get_all_lists':
            $lists = [];
            $files = glob($listsDir . '/*.json');
            
            foreach ($files as $file) {
                $data = json_decode(file_get_contents($file), true);
                if ($data) {
                    $lists[] = [
                        'name' => $data['name'],
                        'count' => $data['count'],
                        'created' => $data['created']
                    ];
                }
            }
            
            // Sort by creation date, newest first
            usort($lists, function($a, $b) {
                return strtotime($b['created']) - strtotime($a['created']);
            });
            
            echo json_encode(['success' => true, 'lists' => $lists]);
            exit;
    }
}

// Get all lists for initial page load
$savedLists = [];
if (is_dir($listsDir)) {
    $files = glob($listsDir . '/*.json');
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data) {
            $savedLists[] = [
                'name' => $data['name'],
                'count' => $data['count'],
                'created' => $data['created']
            ];
        }
    }
    // Sort by creation date, newest first
    usort($savedLists, function($a, $b) {
        return strtotime($b['created']) - strtotime($a['created']);
    });
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Tab Website Opener with Saved Lists</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .url-input-section {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .saved-lists-section {
            padding: 20px;
            background: #f0f8ff;
            border-radius: 10px;
            border: 2px solid #e3f2fd;
        }
        
        textarea {
            width: 100%;
            height: 250px;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        
        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        input[type="text"] {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 600;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .open-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            font-size: 16px;
            padding: 12px 30px;
        }
        
        .open-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .save-btn {
            background: linear-gradient(45deg, #007bff, #6610f2);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }
        
        .load-btn {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
            min-width: 80px;
            padding: 8px 15px;
            font-size: 12px;
        }
        
        .load-btn:hover {
            transform: translateY(-2px);
        }
        
        .delete-btn {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
            min-width: 80px;
            padding: 8px 15px;
            font-size: 12px;
        }
        
        .delete-btn:hover {
            transform: translateY(-2px);
        }
        
        .preset-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .preset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .clear-btn {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }
        
        .clear-btn:hover {
            transform: translateY(-2px);
        }
        
        .info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .saved-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .saved-list-info {
            flex-grow: 1;
        }
        
        .saved-list-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .saved-list-meta {
            font-size: 12px;
            color: #666;
        }
        
        .saved-list-actions {
            display: flex;
            gap: 5px;
        }
        
        .save-new-section {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border: 1px dashed #2196f3;
        }
        
        .save-new-section input {
            width: 200px;
            margin-right: 10px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            body {
                padding: 10px;
            }
            
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .button-group {
                flex-direction: column;
                align-items: center;
            }
            
            button {
                width: 100%;
                max-width: 300px;
            }
            
            .saved-list-item {
                flex-direction: column;
                gap: 10px;
            }
            
            .saved-list-actions {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Multi-Tab Opener Pro</h1>
        
        <div class="info">
            <strong>How to use:</strong> Enter URLs (one per line), save them as named lists, and open all tabs with one click!
        </div>
        
        <div class="warning">
            <strong>Note:</strong> Make sure to allow popups for this page in Firefox settings.
        </div>
        
        <div class="main-content">
            <div class="url-input-section">
                <h3>📝 URL Input</h3>
                <textarea id="urlList" placeholder="https://www.google.com
https://www.github.com
https://www.stackoverflow.com
https://www.mozilla.org
https://www.wikipedia.org"></textarea>
                
                <div class="button-group">
                    <button class="open-btn" onclick="openAllTabs()">
                        🚀 Open All Tabs
                    </button>
                    <button class="clear-btn" onclick="clearUrls()">
                        🗑️ Clear
                    </button>
                </div>
                
                <div class="save-new-section">
                    <h4>💾 Save Current URLs</h4>
                    <input type="text" id="newListName" placeholder="Enter list name..." maxlength="50">
                    <button class="save-btn" onclick="saveCurrentList()">Save List</button>
                </div>
            </div>
            
            <div class="saved-lists-section">
                <h3>📚 Saved Lists</h3>
                <div id="savedListsContainer">
                    <?php if (empty($savedLists)): ?>
                        <p style="text-align: center; color: #666; font-style: italic;">No saved lists yet</p>
                    <?php else: ?>
                        <?php foreach ($savedLists as $list): ?>
                            <div class="saved-list-item">
                                <div class="saved-list-info">
                                    <div class="saved-list-name"><?= htmlspecialchars($list['name']) ?></div>
                                    <div class="saved-list-meta">
                                        <?= $list['count'] ?> URLs • <?= date('M j, Y', strtotime($list['created'])) ?>
                                    </div>
                                </div>
                                <div class="saved-list-actions">
                                    <button class="load-btn" onclick="loadSavedList('<?= htmlspecialchars($list['name']) ?>')">
                                        📁 Load
                                    </button>
                                    <button class="delete-btn" onclick="deleteSavedList('<?= htmlspecialchars($list['name']) ?>')">
                                        🗑️ Delete
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="presets">
            <button class="preset-btn" onclick="loadPreset('social')">📱 Social Media</button>
            <button class="preset-btn" onclick="loadPreset('news')">📰 News Sites</button>
            <button class="preset-btn" onclick="loadPreset('dev')">💻 Developer Tools</button>
            <button class="preset-btn" onclick="loadPreset('productivity')">📊 Productivity</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        // Predefined URL sets
        const presets = {
            social: [
                'https://www.facebook.com',
                'https://www.twitter.com',
                'https://www.instagram.com',
                'https://www.linkedin.com',
                'https://www.reddit.com'
            ],
            news: [
                'https://www.bbc.com/news',
                'https://www.cnn.com',
                'https://www.reuters.com',
                'https://www.npr.org',
                'https://www.theguardian.com'
            ],
            dev: [
                'https://github.com',
                'https://stackoverflow.com',
                'https://developer.mozilla.org',
                'https://codepen.io',
                'https://www.w3schools.com'
            ],
            productivity: [
                'https://gmail.com',
                'https://calendar.google.com',
                'https://drive.google.com',
                'https://trello.com',
                'https://slack.com'
            ]
        };

        async function makeRequest(data) {
            const response = await fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            return await response.json();
        }

        function openAllTabs() {
            const urlList = document.getElementById('urlList').value;
            
            if (!urlList.trim()) {
                showStatus('Please enter at least one URL!', 'error');
                return;
            }
            
            const urls = urlList.split('\n')
                .map(url => url.trim())
                .filter(url => url.length > 0);
            
            if (urls.length === 0) {
                showStatus('No valid URLs found!', 'error');
                return;
            }
            
            // Validate URLs
            const validUrls = urls.filter(url => isValidUrl(url));
            const invalidUrls = urls.filter(url => !isValidUrl(url));
            
            if (invalidUrls.length > 0) {
                showStatus(`Invalid URLs: ${invalidUrls.join(', ')}`, 'error');
                return;
            }
            
            if (validUrls.length > 10) {
                if (!confirm(`You're about to open ${validUrls.length} tabs. Continue?`)) {
                    return;
                }
            }
            
            // Test popup blocker
            const testPopup = window.open('', '_blank', 'width=1,height=1');
            if (!testPopup) {
                showStatus('❌ Popup blocker detected! Please allow popups for this page.', 'error');
                return;
            } else {
                testPopup.close();
            }
            
            showStatus('🚀 Opening tabs...', 'success');
            
            let openedCount = 0;
            validUrls.forEach((url, index) => {
                setTimeout(() => {
                    try {
                        const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
                        if (newWindow) openedCount++;
                        
                        if (index === validUrls.length - 1) {
                            setTimeout(() => {
                                showStatus(`✅ Successfully opened ${openedCount} tabs! 🎉`, 'success');
                            }, 200);
                        }
                    } catch (error) {
                        console.error('Failed to open URL:', url, error);
                    }
                }, index * 50);
            });
        }
        
        function isValidUrl(string) {
            try {
                new URL(string);
                return string.startsWith('http://') || string.startsWith('https://');
            } catch (_) {
                return false;
            }
        }
        
        function clearUrls() {
            if (confirm('Clear all URLs?')) {
                document.getElementById('urlList').value = '';
                document.getElementById('status').style.display = 'none';
            }
        }
        
        function loadPreset(type) {
            if (presets[type]) {
                document.getElementById('urlList').value = presets[type].join('\n');
                showStatus(`Loaded ${presets[type].length} ${type} URLs`, 'success');
            }
        }
        
        async function saveCurrentList() {
            const urlList = document.getElementById('urlList').value;
            const listName = document.getElementById('newListName').value.trim();
            
            if (!urlList.trim()) {
                showStatus('No URLs to save!', 'error');
                return;
            }
            
            if (!listName) {
                showStatus('Please enter a list name!', 'error');
                return;
            }
            
            const urls = urlList.split('\n')
                .map(url => url.trim())
                .filter(url => url.length > 0);
            
            try {
                const result = await makeRequest({
                    action: 'save_list',
                    name: listName,
                    urls: urls
                });
                
                if (result.success) {
                    showStatus(`✅ Saved "${listName}" (${urls.length} URLs)`, 'success');
                    document.getElementById('newListName').value = '';
                    await refreshSavedLists();
                } else {
                    showStatus(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus('❌ Failed to save list', 'error');
                console.error('Save error:', error);
            }
        }
        
        async function loadSavedList(listName) {
            try {
                const result = await makeRequest({
                    action: 'load_list',
                    name: listName
                });
                
                if (result.success) {
                    document.getElementById('urlList').value = result.data.urls.join('\n');
                    showStatus(`✅ Loaded "${listName}" (${result.data.count} URLs)`, 'success');
                } else {
                    showStatus(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus('❌ Failed to load list', 'error');
                console.error('Load error:', error);
            }
        }
        
        async function deleteSavedList(listName) {
            if (!confirm(`Delete list "${listName}"?`)) {
                return;
            }
            
            try {
                const result = await makeRequest({
                    action: 'delete_list',
                    name: listName
                });
                
                if (result.success) {
                    showStatus(`🗑️ Deleted "${listName}"`, 'success');
                    await refreshSavedLists();
                } else {
                    showStatus(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus('❌ Failed to delete list', 'error');
                console.error('Delete error:', error);
            }
        }
        
        async function refreshSavedLists() {
            try {
                const result = await makeRequest({
                    action: 'get_all_lists'
                });
                
                if (result.success) {
                    const container = document.getElementById('savedListsContainer');
                    
                    if (result.lists.length === 0) {
                        container.innerHTML = '<p style="text-align: center; color: #666; font-style: italic;">No saved lists yet</p>';
                    } else {
                        container.innerHTML = result.lists.map(list => `
                            <div class="saved-list-item">
                                <div class="saved-list-info">
                                    <div class="saved-list-name">${escapeHtml(list.name)}</div>
                                    <div class="saved-list-meta">
                                        ${list.count} URLs • ${new Date(list.created).toLocaleDateString('en-US', {month: 'short', day: 'numeric', year: 'numeric'})}
                                    </div>
                                </div>
                                <div class="saved-list-actions">
                                    <button class="load-btn" onclick="loadSavedList('${escapeHtml(list.name)}')">
                                        📁 Load
                                    </button>
                                    <button class="delete-btn" onclick="deleteSavedList('${escapeHtml(list.name)}')">
                                        🗑️ Delete
                                    </button>
                                </div>
                            </div>
                        `).join('');
                    }
                }
            } catch (error) {
                console.error('Refresh error:', error);
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }
        
        // Keyboard shortcuts
        document.getElementById('urlList').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                openAllTabs();
            }
        });
        
        document.getElementById('newListName').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                saveCurrentList();
            }
        });
    </script>
</body>
</html>