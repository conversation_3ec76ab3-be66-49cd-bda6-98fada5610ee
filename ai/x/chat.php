<?php
header("Content-Type: application/json");

// Load your API key (if using dotenv, otherwise replace with your key directly)
$apiKey = 'FzXZM58QWWy57aycpEAfun1qzkTTO9Vpl2bqgtDqKcw13oBw7cAVETVWE2PrNFq19mvgxp8UH9SKLaVn';

// Get the user message from the POST request
$input = json_decode(file_get_contents('php://input'), true);
$userMessage = $input['message'] ?? '';

if ($userMessage) {
    // Prepare the request to the OpenAI API
    $url = 'https://api.x.ai/v1/chat/completions';
    $data = [
        "model" => "grok-beta",
        "messages" => [
            ["role" => "user", "content" => $userMessage]
        ]
    ];

    // Set up the headers
    $options = [
        "http" => [
            "header"  => "Content-Type: application/json\r\n" .
                         "Authorization: Bearer " . $apiKey . "\r\n",
            "method"  => "POST",
            "content" => json_encode($data),
        ]
    ];
    $context = stream_context_create($options);

    // Make the API request
    $result = file_get_contents($url, false, $context);
    if ($result === FALSE) {
        echo json_encode(["response" => "Error connecting to API"]);
        exit;
    }

    // Parse and send the response back to the front-end
    $response = json_decode($result, true);
    $botMessage = $response['choices'][0]['message']['content'];
    echo json_encode(["response" => $botMessage]);
} else {
    echo json_encode(["response" => "No message provided"]);
}
?>
