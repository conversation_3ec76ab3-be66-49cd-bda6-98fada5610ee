<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Tab Website Opener</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .url-input-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }

        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
        }

        button {
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .open-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .open-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .preset-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .preset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .clear-btn {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .clear-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }

        .info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-size: 14px;
        }

        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-size: 14px;
        }

        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 2em;
            }
            .button-group {
                flex-direction: column;
                align-items: center;
            }
            button {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Multi-Tab Opener</h1>

        <div class="info">
            <strong>How to use:</strong> Enter one URL per line in the text area below, then click "Open All Tabs".
            URLs should start with http:// or https://
        </div>

        <div class="warning">
            <strong>Note:</strong> Make sure to allow popups for this page in your browser settings.
            Firefox may ask for permission to open multiple tabs.
        </div>

        <div class="url-input-section">
            <label for="urlList"><strong>Enter URLs (one per line):</strong></label>
            <textarea id="urlList" placeholder="https://www.google.com
https://www.github.com
https://www.stackoverflow.com
https://www.mozilla.org
https://www.wikipedia.org"></textarea>
        </div>

        <div class="button-group">
            <button class="open-btn" onclick="openAllTabs()">
                🚀 Open All Tabs
            </button>
            <button class="clear-btn" onclick="clearUrls()">
                🗑️ Clear All
            </button>
        </div>

        <div class="presets">
            <button class="preset-btn" onclick="loadPreset('social')">
                📱 Social Media
            </button>
            <button class="preset-btn" onclick="loadPreset('news')">
                📰 News Sites
            </button>
            <button class="preset-btn" onclick="loadPreset('dev')">
                💻 Developer Tools
            </button>
            <button class="preset-btn" onclick="loadPreset('productivity')">
                📊 Productivity
            </button>
        </div>

        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        // Predefined URL sets
        const presets = {
            social: [
                'https://www.facebook.com',
                'https://www.twitter.com',
                'https://www.instagram.com',
                'https://www.linkedin.com',
                'https://www.reddit.com'
            ],
            news: [
                'https://www.bbc.com/news',
                'https://www.cnn.com',
                'https://www.reuters.com',
                'https://www.npr.org',
                'https://www.theguardian.com'
            ],
            dev: [
                'https://github.com',
                'https://stackoverflow.com',
                'https://developer.mozilla.org',
                'https://codepen.io',
                'https://www.w3schools.com'
            ],
            productivity: [
                'https://gmail.com',
                'https://calendar.google.com',
                'https://drive.google.com',
                'https://trello.com',
                'https://slack.com'
            ]
        };

        function openAllTabs() {
            const urlList = document.getElementById('urlList').value;
            const statusDiv = document.getElementById('status');

            if (!urlList.trim()) {
                showStatus('Please enter at least one URL!', 'error');
                return;
            }

            // Split by lines and filter out empty lines
            const urls = urlList.split('\n')
                .map(url => url.trim())
                .filter(url => url.length > 0);

            if (urls.length === 0) {
                showStatus('No valid URLs found!', 'error');
                return;
            }

            // Validate URLs
            const validUrls = [];
            const invalidUrls = [];

            urls.forEach(url => {
                if (isValidUrl(url)) {
                    validUrls.push(url);
                } else {
                    invalidUrls.push(url);
                }
            });

            if (invalidUrls.length > 0) {
                showStatus(`Invalid URLs found: ${invalidUrls.join(', ')}. Please check the format.`, 'error');
                return;
            }

            // Ask for confirmation if opening many tabs
            if (validUrls.length > 10) {
                if (!confirm(`You're about to open ${validUrls.length} tabs. Continue?`)) {
                    return;
                }
            }

            // Open all tabs
            let openedCount = 0;
            let failedCount = 0;

            validUrls.forEach((url, index) => {
                setTimeout(() => {
                    try {
                        const newWindow = window.open(url, '_blank');
                        if (newWindow) {
                            openedCount++;
                        } else {
                            failedCount++;
                        }

                        // Show final status after all attempts
                        if (index === validUrls.length - 1) {
                            setTimeout(() => {
                                if (failedCount > 0) {
                                    showStatus(`Opened ${openedCount} tabs. ${failedCount} failed (check popup blocker).`, 'error');
                                } else {
                                    showStatus(`Successfully opened ${openedCount} tabs! 🎉`, 'success');
                                }
                            }, 100);
                        }
                    } catch (error) {
                        failedCount++;
                        console.error('Failed to open URL:', url, error);
                    }
                }, index * 100); // Small delay between each tab opening
            });
        }

        function isValidUrl(string) {
            try {
                new URL(string);
                return string.startsWith('http://') || string.startsWith('https://');
            } catch (_) {
                return false;
            }
        }

        function clearUrls() {
            if (confirm('Clear all URLs?')) {
                document.getElementById('urlList').value = '';
                document.getElementById('status').style.display = 'none';
            }
        }

        function loadPreset(type) {
            if (presets[type]) {
                document.getElementById('urlList').value = presets[type].join('\n');
                showStatus(`Loaded ${presets[type].length} ${type} URLs`, 'success');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';

            // Auto-hide success messages after 3 seconds
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }

        // Handle Enter key in textarea (Ctrl+Enter to open tabs)
        document.getElementById('urlList').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                openAllTabs();
            }
        });

        // Check if popups are blocked on page load
        window.addEventListener('load', function() {
            // Test popup capability
            const testPopup = window.open('', '_blank', 'width=1,height=1');
            if (testPopup) {
                testPopup.close();
            } else {
                showStatus('Popup blocker detected! Please allow popups for this page.', 'error');
            }
        });
    </script>

    <?php
    // PHP section (optional) - can be used for server-side URL validation or logging
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['urls'])) {
        $urls = explode('\n', $_POST['urls']);
        $validUrls = array();

        foreach ($urls as $url) {
            $url = trim($url);
            if (filter_var($url, FILTER_VALIDATE_URL)) {
                $validUrls[] = $url;
            }
        }

        // Log the action (optional)
        error_log("Multi-tab opener used: " . count($validUrls) . " URLs opened");

        // Return JSON response for AJAX calls
        if (isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'validUrls' => $validUrls,
                'totalUrls' => count($validUrls)
            ]);
            exit;
        }
    }
    ?>
</body>
</html>
