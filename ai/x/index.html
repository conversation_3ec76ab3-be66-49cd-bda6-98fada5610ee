<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Interface</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="chat-container">
        <div id="chat-output"></div>
        <input type="text" id="chat-input" placeholder="Type your message...">
        <button onclick="sendMessage()">Send</button>
    </div>

    <script>
        function sendMessage() {
            const message = document.getElementById('chat-input').value;
            const output = document.getElementById('chat-output');

            fetch('chat.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                const userMessage = `<div class="user-message">${message}</div>`;
                const botMessage = `<div class="bot-message">${data.response}</div>`;
                output.innerHTML += userMessage + botMessage;
                document.getElementById('chat-input').value = '';
            })
            .catch(error => console.error('Error:', error));
        }
    </script>
</body>
</html>
