<?php
/* whre to put
 vi /home/<USER>/hi/tool/ipv6-gen-ovhe.php
 https://hi.hlsn.net/tool/ipv6-gen-o1.php

vi /wks/wfits/hi/tool/ipv6-gen-o1.php
 http://hi.d/tool/ipv6-gen-o1.php
 chmod o+rw  /WD/KS-LE-1/ipv6-gen-ks.php
  rsync -a   /WD/KS-LE-1/ipv6-gen-ks.php /wks/wfits/hi/tool/; chown :www-data /wks/wfits/hi/tool/ipv6-gen-ks.php; chmod 440 /wks/wfits/hi/tool/ipv6-gen-ks.php
 http://hi.d/tool/ipv6-gen-ks.php

  nginx version: new
    rsync -a   /WD/KS-LE-1/ipv6-gen-ks.php /wks/h/tool/; chown :www-data /wks/h/tool/ipv6-gen-ks.php; chmod 440 /wks/h/tool/ipv6-gen-ks.php
 http://h.f/tool/ipv6-gen-ks.php
 
*/
?>

<?php
function ipv6ovhi3(){
    $length = 4;
    $randomString = substr(str_shuffle("0123456789abcdef"), 0, $length);
    $randomString2 = substr(str_shuffle("0123456789abcdef"), 0, $length);
    $randomString3 = substr(str_shuffle("0123456789abcdef"), 0, $length);
    $randomString4 = substr(str_shuffle("0123456789abcdef"), 0, $length);
    $ipv6 = $randomString . ":" . $randomString2 . ":" . $randomString3 . ":" . $randomString4;
    $ipv6up = "up ip -6 addr add 2001:41d0:a:2e07:" . $ipv6 . "/64 dev \$IFACE";
    $ipv6down = "down ip -6 addr del 2001:41d0:a:2e07:" . $ipv6 . "/64 dev \$IFACE";
    echo $ipv6up . "<br />";
    echo $ipv6down . "<br />";
                    }
$num = 32;
for($i =0; $i < $num; $i++){
    $array[] = ipv6ovhi3();
                           }
    $array;
?>
